// API Keys Configuration
// Add your API keys here directly

export const API_KEYS = {
  // Add your AssemblyAI API key here
  ASSEMBLYAI: '********************************',

  // Add your OpenAI API key here
  OPENAI: 'YOUR_OPENAI_API_KEY_HERE',

  // Gemini key (already working)
  GEMINI: import.meta.env.VITE_GEMINI_API_KEY || '',

  // Add your Google Cloud API key here (for Translate and TTS)
  // Get a free API key at: https://console.cloud.google.com/apis/credentials
  // Enable: Cloud Translation API and Cloud Text-to-Speech API
  GOOGLE_CLOUD: 'AIzaSyCq6ZAL1TQiFoU1Z8kyR7YjNdkTMabPMtU'
};

// Helper function to get API keys
export function getApiKey(service: 'assemblyai' | 'openai' | 'gemini' | 'google-cloud' | 'google-translate'): string {
  switch (service) {
    case 'assemblyai':
      return API_KEYS.ASSEMBLYAI;
    case 'openai':
      return API_KEYS.OPENAI;
    case 'gemini':
      return API_KEYS.GEMINI;
    case 'google-cloud':
    case 'google-translate':
      return API_KEYS.GOOGLE_CLOUD;
    default:
      return '';
  }
}

// Check if API key is configured
export function hasApiKey(service: 'assemblyai' | 'openai' | 'gemini' | 'google-cloud' | 'google-translate'): boolean {
  const key = getApiKey(service);
  return key !== '' && !key.includes('YOUR_') && key.length > 10;
}
