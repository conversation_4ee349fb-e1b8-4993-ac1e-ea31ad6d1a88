// Simple verification script to prove translation is working
// Run this in browser console to see exactly what's happening

export async function verifyTranslationIsWorking() {
  console.log('🔍 VERIFYING TRANSLATION AGENT IS WORKING...');
  console.log('================================================');
  
  try {
    // Step 1: Check if we have a project
    const { projectDataManager } = await import('./utils/projectDataManager');
    const project = projectDataManager.getCurrentProject();
    
    if (!project) {
      console.log('❌ FAIL: No project found');
      return false;
    }
    
    console.log('✅ PASS: Project found');
    console.log(`   📁 Project: ${project.videoFileName}`);
    console.log(`   🆔 Analysis ID: ${project.analysisId}`);
    console.log(`   🎬 Scenes: ${project.scenes?.length || 0}`);
    
    if (!project.scenes || project.scenes.length === 0) {
      console.log('❌ FAIL: No scenes in project');
      return false;
    }
    
    const scene = project.scenes[0];
    console.log('✅ PASS: Scene found');
    console.log(`   🎬 Scene: ${scene.title}`);
    console.log(`   ⏱️ Duration: ${scene.duration}s`);
    console.log(`   📍 Start: ${scene.start}s`);
    
    // Step 2: Create mock transcription if needed
    const { transcriptionStorageService } = await import('./services/transcriptionStorageService');
    
    if (!transcriptionStorageService.hasTranscription(project.analysisId)) {
      console.log('📝 Creating mock transcription...');
      
      const mockSegments = [
        {
          start: scene.start,
          end: scene.start + Math.min(3, scene.duration),
          text: "Hello, this is a test video for translation.",
          words: []
        }
      ];
      
      const mockMetadata = {
        language: 'en',
        duration: scene.duration,
        word_count: 10,
        segment_count: 1,
        transcription_method: 'verification-test',
        created_at: Date.now()
      };
      
      transcriptionStorageService.storeFullTranscription(
        project.analysisId,
        mockSegments,
        mockMetadata
      );
      
      console.log('✅ PASS: Mock transcription created');
    } else {
      console.log('✅ PASS: Transcription already exists');
    }
    
    // Step 3: Test translation service directly
    console.log('🌐 Testing translation service...');
    const { translationService } = await import('./services/translationService');
    
    const result = await translationService.translateScene(
      {
        analysisId: project.analysisId,
        sceneStart: scene.start,
        sceneDuration: scene.duration
      },
      {
        targetLanguage: 'es',
        sourceLanguage: 'en',
        includeSubtitles: true,
        includeDubbedAudio: false // Skip audio to avoid API errors
      }
    );
    
    console.log('✅ PASS: Translation service completed');
    console.log(`   📝 Original subtitles: ${result.originalSubtitles.length}`);
    console.log(`   🌐 Translated subtitles: ${result.translatedSubtitles.length}`);
    console.log(`   ⚡ Processing time: ${result.processingTime}ms`);
    console.log(`   🎯 Method: ${result.method}`);
    
    if (result.originalSubtitles.length > 0) {
      console.log('📝 ORIGINAL TEXT:');
      result.originalSubtitles.forEach((sub, i) => {
        console.log(`   ${i + 1}. "${sub.text}"`);
      });
    }
    
    if (result.translatedSubtitles.length > 0) {
      console.log('🌐 TRANSLATED TEXT (Spanish):');
      result.translatedSubtitles.forEach((sub, i) => {
        console.log(`   ${i + 1}. "${sub.text}"`);
      });
      
      console.log('🎉 SUCCESS: Translation is working!');
      console.log('   The translation agent successfully:');
      console.log('   ✅ Found/created transcription');
      console.log('   ✅ Translated text to Spanish');
      console.log('   ✅ Returned proper subtitle format');
      
      return true;
    } else {
      console.log('❌ FAIL: No translated subtitles generated');
      return false;
    }
    
  } catch (error) {
    console.error('❌ FAIL: Verification failed:', error);
    return false;
  }
}

export async function showTranslationInTimeline() {
  console.log('🎬 CHECKING TIMELINE FOR TRANSLATIONS...');
  console.log('==========================================');

  try {
    // Check timeline state
    const timelineState = localStorage.getItem('timeline-state');
    if (!timelineState) {
      console.log('❌ No timeline state found');
      return;
    }

    const parsed = JSON.parse(timelineState);
    if (!parsed.tracks) {
      console.log('❌ No tracks in timeline');
      return;
    }

    console.log(`📊 Timeline has ${parsed.tracks.length} tracks`);

    let foundTranslations = false;
    let foundSubtitles = false;
    let foundDubbedAudio = false;

    for (let i = 0; i < parsed.tracks.length; i++) {
      const track = parsed.tracks[i];
      if (!track.items) continue;

      for (let j = 0; j < track.items.length; j++) {
        const item = track.items[j];

        if (item.metadata?.aiGenerated) {
          if (item.metadata.translationType === 'subtitle') {
            foundTranslations = true;
            console.log(`🌐 TRANSLATION FOUND in track ${i}, item ${j}:`);
            console.log(`   📝 Text: "${item.details?.text}"`);
            console.log(`   🎨 Color: ${item.details?.color}`);
            console.log(`   📍 Position: ${item.details?.top}px from top`);
            console.log(`   🌍 Language: ${item.metadata?.targetLanguage}`);
          } else if (item.metadata.translationType === 'audio') {
            foundDubbedAudio = true;
            console.log(`🎤 DUBBED AUDIO FOUND in track ${i}, item ${j}:`);
            console.log(`   🔊 Source: ${item.details?.src}`);
            console.log(`   🔊 Volume: ${item.details?.volume}`);
            console.log(`   🌍 Language: ${item.metadata?.targetLanguage}`);
            console.log(`   ⏱️ Duration: ${item.trim?.to}ms`);
          } else if (item.type === 'caption') {
            foundSubtitles = true;
            console.log(`📝 SUBTITLE FOUND in track ${i}, item ${j}:`);
            console.log(`   📝 Text: "${item.details?.text}"`);
            console.log(`   🎨 Color: ${item.details?.color}`);
          }
        }
      }
    }

    console.log('\n📊 SUMMARY:');
    console.log(`   🌐 Translation subtitles: ${foundTranslations ? '✅' : '❌'}`);
    console.log(`   📝 Original subtitles: ${foundSubtitles ? '✅' : '❌'}`);
    console.log(`   🎤 Dubbed audio: ${foundDubbedAudio ? '✅' : '❌'}`);

    if (foundTranslations && foundDubbedAudio) {
      console.log('🎉 SUCCESS: Full translation with audio is in the timeline!');
    } else if (foundTranslations) {
      console.log('⚠️ Found translations but no dubbed audio');
    } else {
      console.log('❌ No translations found in timeline');
      console.log('💡 Try connecting a translation agent to a scene');
    }

  } catch (error) {
    console.error('❌ Failed to check timeline:', error);
  }
}

export async function testDubbedAudioDirectly() {
  console.log('🎤 TESTING DUBBED AUDIO DIRECTLY...');
  console.log('===================================');

  try {
    // Test Web Speech API directly
    if (!('speechSynthesis' in window)) {
      console.log('❌ Web Speech API not supported in this browser');
      return false;
    }

    console.log('✅ Web Speech API is available');

    // Get available voices
    const voices = speechSynthesis.getVoices();
    console.log(`🗣️ Available voices: ${voices.length}`);

    const spanishVoices = voices.filter(voice => voice.lang.startsWith('es'));
    console.log(`🇪🇸 Spanish voices: ${spanishVoices.length}`);

    if (spanishVoices.length > 0) {
      console.log('🗣️ Spanish voices found:');
      spanishVoices.forEach((voice, i) => {
        console.log(`   ${i + 1}. ${voice.name} (${voice.lang})`);
      });
    }

    // Test speaking Spanish text
    console.log('🎤 Testing Spanish speech...');
    const utterance = new SpeechSynthesisUtterance('Hola, esto es una prueba de traducción de audio.');
    utterance.lang = 'es-ES';
    utterance.rate = 1.0;

    if (spanishVoices.length > 0) {
      utterance.voice = spanishVoices[0];
      console.log(`🗣️ Using voice: ${spanishVoices[0].name}`);
    }

    return new Promise((resolve) => {
      utterance.onend = () => {
        console.log('✅ Spanish speech test completed successfully!');
        resolve(true);
      };

      utterance.onerror = (event) => {
        console.error('❌ Speech test failed:', event);
        resolve(false);
      };

      speechSynthesis.speak(utterance);
      console.log('🗣️ Speaking Spanish text now...');
    });

  } catch (error) {
    console.error('❌ Dubbed audio test failed:', error);
    return false;
  }
}

// Export for browser console
if (typeof window !== 'undefined') {
  (window as any).verifyTranslationIsWorking = verifyTranslationIsWorking;
  (window as any).showTranslationInTimeline = showTranslationInTimeline;
  (window as any).testDubbedAudioDirectly = testDubbedAudioDirectly;

  console.log('🔧 Translation verification functions available:');
  console.log('   - window.verifyTranslationIsWorking() - Prove translation works');
  console.log('   - window.showTranslationInTimeline() - Check timeline content');
  console.log('   - window.testDubbedAudioDirectly() - Test Spanish speech audio');
}
