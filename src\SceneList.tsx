// src/components/SceneList.tsx
import React from 'react';
import type { AnalyzedScene, ApiAnalysisResponse } from './types';
import { formatTime } from './utils';
import { transcriptionStorageService } from './services/transcriptionStorageService';

interface SceneListProps {
  analysisData: ApiAnalysisResponse | null;
  // selectedSceneId?: string | null; // If you want to highlight based on an external selection
  onSceneSelect: (scene: AnalyzedScene) => void;
}

const TransitionIcon: React.FC<{ type?: string }> = ({ type }) => { // Made type optional
  switch (type?.toLowerCase()) {
    case 'fade-in': return <span title="Fade In">☀️</span>;
    case 'fade-out': return <span title="Fade Out">🌙</span>;
    case 'cut': return <span title="Cut">✂️</span>;
    default: return <span title="Transition">🎬</span>; // Generic for unknown or 'none'
  }
};

const SceneList: React.FC<SceneListProps> = ({ analysisData, onSceneSelect }) => {
  if (!analysisData || !analysisData.scenes || analysisData.scenes.length === 0) {
    return <p className="no-scenes-message">No scenes analyzed yet, or no scenes found.</p>;
  }

  // Check if transcription is available for this video
  const hasTranscription = transcriptionStorageService.hasTranscription(analysisData.analysisId);
  const transcriptionMetadata = hasTranscription ? transcriptionStorageService.getTranscriptionMetadata(analysisData.analysisId) : null;

  return (
    <div className="scene-list-container">
      <h5 className="scene-list-title">Video: {analysisData.fileName.length > 25 ? analysisData.fileName.substring(0,22) + "..." : analysisData.fileName}</h5>

      {/* Transcription Status */}
      <div className="transcription-status" style={{
        padding: '8px',
        margin: '8px 0',
        borderRadius: '4px',
        fontSize: '12px',
        backgroundColor: hasTranscription ? '#e8f5e8' : '#fff3cd',
        border: `1px solid ${hasTranscription ? '#4caf50' : '#ffc107'}`,
        color: hasTranscription ? '#2e7d32' : '#856404'
      }}>
        {hasTranscription ? (
          <div>
            <span>✅ Full video transcription available</span>
            {transcriptionMetadata && (
              <div style={{ marginTop: '4px', fontSize: '11px', opacity: 0.8 }}>
                📝 {transcriptionMetadata.segment_count} segments • {transcriptionMetadata.word_count} words • {transcriptionMetadata.transcription_method}
              </div>
            )}
          </div>
        ) : (
          <span>⚠️ No transcription available - subtitle/translation agents will use real-time processing</span>
        )}
      </div>

      <ul className="scene-list">
        {analysisData.scenes.map((scene) => {
          // Check if this specific scene has subtitle data available
          const sceneHasSubtitles = hasTranscription && transcriptionStorageService.getSceneSubtitles(
            analysisData.analysisId,
            scene.start,
            scene.duration
          ).length > 0;

          return (
            <li
              key={scene.sceneId} // Use sceneId for the key
              className={`scene-list-item`} // Removed selected class logic, App.tsx handles node selection
              onClick={() => onSceneSelect(scene)}
            >
              <div className="scene-item-index">#{scene.scene_index + 1}</div>
              <div className="scene-item-timecode">
                {formatTime(scene.start)} - {formatTime(scene.end)}
              </div>
              <div className="scene-item-duration">({formatTime(scene.duration)})</div>
              <div className="scene-item-transition">
                <TransitionIcon type={scene.transition_type} />
              </div>
              {/* Scene-specific transcription indicator */}
              {sceneHasSubtitles && (
                <div className="scene-transcription-indicator" style={{
                  fontSize: '10px',
                  color: '#4caf50',
                  marginTop: '2px'
                }} title="Transcription available for this scene">
                  📝 Ready
                </div>
              )}
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default SceneList;