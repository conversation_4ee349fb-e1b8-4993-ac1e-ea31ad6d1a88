// src/services/transcriptionStorageService.ts
// Service for storing and retrieving full video transcriptions from AssemblyAI

import type { SubtitleCue } from '../components/SubtitleOverlay';

export interface TranscriptionSegment {
  start: number;
  end: number;
  text: string;
  words?: Array<{
    text: string;
    start: number;
    end: number;
    confidence?: number;
  }>;
}

export interface TranscriptionMetadata {
  language: string;
  duration: number;
  word_count: number;
  segment_count: number;
  transcription_method: string;
  created_at: number;
}

export interface StoredTranscription {
  analysisId: string;
  segments: TranscriptionSegment[];
  metadata: TranscriptionMetadata;
  storedAt: number;
}

class TranscriptionStorageService {
  private readonly STORAGE_KEY_PREFIX = 'transcription-';
  private readonly METADATA_KEY_PREFIX = 'transcription-meta-';

  // Store full video transcription from analysis result
  storeFullTranscription(
    analysisId: string, 
    segments: TranscriptionSegment[], 
    metadata: TranscriptionMetadata
  ): void {
    try {
      const transcription: StoredTranscription = {
        analysisId,
        segments,
        metadata,
        storedAt: Date.now()
      };

      const storageKey = `${this.STORAGE_KEY_PREFIX}${analysisId}`;
      const metaKey = `${this.METADATA_KEY_PREFIX}${analysisId}`;

      // Store transcription segments
      localStorage.setItem(storageKey, JSON.stringify(transcription));
      
      // Store metadata separately for quick access
      localStorage.setItem(metaKey, JSON.stringify(metadata));

      console.log(`💾 Stored full transcription for analysis ${analysisId}:`, {
        segments: segments.length,
        duration: metadata.duration,
        words: metadata.word_count
      });

    } catch (error) {
      console.error(`❌ Failed to store transcription for ${analysisId}:`, error);
    }
  }

  // Check if transcription exists for analysis
  hasTranscription(analysisId: string): boolean {
    const storageKey = `${this.STORAGE_KEY_PREFIX}${analysisId}`;
    return localStorage.getItem(storageKey) !== null;
  }

  // Get full transcription for analysis
  getFullTranscription(analysisId: string): StoredTranscription | null {
    try {
      const storageKey = `${this.STORAGE_KEY_PREFIX}${analysisId}`;
      const stored = localStorage.getItem(storageKey);
      
      if (!stored) {
        return null;
      }

      return JSON.parse(stored);
    } catch (error) {
      console.error(`❌ Failed to load transcription for ${analysisId}:`, error);
      return null;
    }
  }

  // Get transcription metadata only (faster)
  getTranscriptionMetadata(analysisId: string): TranscriptionMetadata | null {
    try {
      const metaKey = `${this.METADATA_KEY_PREFIX}${analysisId}`;
      const stored = localStorage.getItem(metaKey);
      
      if (!stored) {
        return null;
      }

      return JSON.parse(stored);
    } catch (error) {
      console.error(`❌ Failed to load transcription metadata for ${analysisId}:`, error);
      return null;
    }
  }

  // Get scene-specific subtitles from full transcription
  getSceneSubtitles(
    analysisId: string, 
    sceneStart: number, 
    sceneDuration: number
  ): SubtitleCue[] {
    try {
      const transcription = this.getFullTranscription(analysisId);
      
      if (!transcription) {
        console.log(`⚠️ No transcription found for analysis ${analysisId}`);
        return [];
      }

      const sceneEnd = sceneStart + sceneDuration;
      const sceneSubtitles: SubtitleCue[] = [];

      // Filter segments that overlap with the scene
      for (const segment of transcription.segments) {
        // Check if segment overlaps with scene timeframe
        if (segment.start < sceneEnd && segment.end > sceneStart) {
          // Adjust timing to be relative to scene start
          const adjustedStartTime = Math.max(0, segment.start - sceneStart);
          const adjustedEndTime = Math.min(sceneDuration, segment.end - sceneStart);

          // Only include if there's meaningful overlap
          if (adjustedEndTime > adjustedStartTime && adjustedEndTime > 0) {
            sceneSubtitles.push({
              id: `scene-${analysisId}-${segment.start}`,
              startTime: adjustedStartTime,
              endTime: adjustedEndTime,
              text: segment.text.trim()
            });
          }
        }
      }

      console.log(`📝 Generated ${sceneSubtitles.length} scene subtitles from transcription`, {
        analysisId,
        sceneStart,
        sceneDuration,
        totalSegments: transcription.segments.length
      });

      return sceneSubtitles;

    } catch (error) {
      console.error(`❌ Failed to generate scene subtitles:`, error);
      return [];
    }
  }

  // Get all available transcriptions (for debugging/management)
  getAllTranscriptions(): StoredTranscription[] {
    const transcriptions: StoredTranscription[] = [];
    
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.STORAGE_KEY_PREFIX)) {
          const stored = localStorage.getItem(key);
          if (stored) {
            transcriptions.push(JSON.parse(stored));
          }
        }
      }
    } catch (error) {
      console.error('❌ Failed to load all transcriptions:', error);
    }

    return transcriptions;
  }

  // Clean up old transcriptions (optional)
  cleanupOldTranscriptions(maxAgeMs: number = 7 * 24 * 60 * 60 * 1000): void {
    try {
      const now = Date.now();
      const keysToRemove: string[] = [];

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.STORAGE_KEY_PREFIX)) {
          const stored = localStorage.getItem(key);
          if (stored) {
            const transcription: StoredTranscription = JSON.parse(stored);
            if (now - transcription.storedAt > maxAgeMs) {
              keysToRemove.push(key);
              // Also remove metadata
              const metaKey = key.replace(this.STORAGE_KEY_PREFIX, this.METADATA_KEY_PREFIX);
              keysToRemove.push(metaKey);
            }
          }
        }
      }

      keysToRemove.forEach(key => localStorage.removeItem(key));
      
      if (keysToRemove.length > 0) {
        console.log(`🧹 Cleaned up ${keysToRemove.length / 2} old transcriptions`);
      }

    } catch (error) {
      console.error('❌ Failed to cleanup old transcriptions:', error);
    }
  }

  // Remove specific transcription
  removeTranscription(analysisId: string): void {
    try {
      const storageKey = `${this.STORAGE_KEY_PREFIX}${analysisId}`;
      const metaKey = `${this.METADATA_KEY_PREFIX}${analysisId}`;
      
      localStorage.removeItem(storageKey);
      localStorage.removeItem(metaKey);
      
      console.log(`🗑️ Removed transcription for analysis ${analysisId}`);
    } catch (error) {
      console.error(`❌ Failed to remove transcription for ${analysisId}:`, error);
    }
  }

  // Get storage usage info
  getStorageInfo(): { transcriptionCount: number; estimatedSizeMB: number } {
    let transcriptionCount = 0;
    let totalSize = 0;

    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.STORAGE_KEY_PREFIX)) {
          transcriptionCount++;
          const value = localStorage.getItem(key);
          if (value) {
            totalSize += value.length * 2; // Rough estimate (UTF-16)
          }
        }
      }
    } catch (error) {
      console.error('❌ Failed to calculate storage info:', error);
    }

    return {
      transcriptionCount,
      estimatedSizeMB: Math.round(totalSize / (1024 * 1024) * 100) / 100
    };
  }
}

// Export singleton instance
export const transcriptionStorageService = new TranscriptionStorageService();
export default transcriptionStorageService;
