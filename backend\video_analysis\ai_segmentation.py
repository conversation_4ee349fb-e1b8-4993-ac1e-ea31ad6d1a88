# --- START OF FILE ai_segmentation.py ---

import json
import os
import sys
import cv2
import subprocess
import tempfile
import time
import requests
from typing import List, Dict, Any, Optional
from scenedetect import VideoManager, SceneManager, FrameTimecode
from scenedetect.detectors import ContentDetector, ThresholdDetector
from detect_scenes import detect_scenes  # Import existing cut-based detection as fallback

# AssemblyAI configuration
ASSEMBLYAI_API_KEY = "********************************"  # Your API key
ASSEMBLYAI_UPLOAD_URL = "https://api.assemblyai.com/v2/upload"
ASSEMBLYAI_TRANSCRIPT_URL = "https://api.assemblyai.com/v2/transcript"

def extract_audio_for_transcription(video_path: str) -> Optional[str]:
    """
    Extract audio from video for transcription using FFmpeg.
    Returns path to temporary audio file or None if extraction fails.
    """
    try:
        # Create temporary audio file
        temp_audio = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
        temp_audio_path = temp_audio.name
        temp_audio.close()

        # Use FFmpeg to extract audio
        command = [
            'ffmpeg', '-i', video_path,
            '-vn',  # No video
            '-acodec', 'pcm_s16le',  # PCM 16-bit little-endian
            '-ar', '16000',  # 16kHz sample rate (good for Whisper)
            '-ac', '1',  # Mono
            '-y',  # Overwrite output file
            temp_audio_path
        ]

        result = subprocess.run(command, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Warning: Failed to extract audio: {result.stderr}", file=sys.stderr)
            os.unlink(temp_audio_path)
            return None

        return temp_audio_path

    except Exception as e:
        print(f"Warning: Audio extraction failed: {str(e)}", file=sys.stderr)
        return None

def transcribe_audio_assemblyai(audio_path: str) -> Optional[List[Dict[str, Any]]]:
    """
    Transcribe audio using AssemblyAI API with word-level timestamps.
    Returns list of segments with timestamps or None if transcription fails.
    """
    try:
        if not ASSEMBLYAI_API_KEY or ASSEMBLYAI_API_KEY == "YOUR_ASSEMBLYAI_API_KEY_HERE":
            print("Warning: AssemblyAI API key not configured", file=sys.stderr)
            return None

        print("Starting AssemblyAI transcription...", file=sys.stderr)

        # Step 1: Upload audio file
        print("Uploading audio to AssemblyAI...", file=sys.stderr)

        with open(audio_path, 'rb') as audio_file:
            upload_response = requests.post(
                ASSEMBLYAI_UPLOAD_URL,
                headers={'authorization': ASSEMBLYAI_API_KEY},
                files={'file': audio_file}
            )

        if upload_response.status_code != 200:
            print(f"Upload failed: {upload_response.status_code} - {upload_response.text}", file=sys.stderr)
            return None

        upload_url = upload_response.json()['upload_url']
        print("Audio uploaded successfully", file=sys.stderr)

        # Step 2: Request transcription with word-level timestamps
        print("Requesting transcription...", file=sys.stderr)

        transcript_request = {
            'audio_url': upload_url,
            'language_code': 'en',  # Auto-detect or specify language
            'punctuate': True,
            'format_text': True,
            'word_boost': ['video', 'scene', 'timeline', 'edit'],
            'boost_param': 'high'
        }

        transcript_response = requests.post(
            ASSEMBLYAI_TRANSCRIPT_URL,
            headers={'authorization': ASSEMBLYAI_API_KEY, 'content-type': 'application/json'},
            json=transcript_request
        )

        if transcript_response.status_code != 200:
            print(f"Transcription request failed: {transcript_response.status_code} - {transcript_response.text}", file=sys.stderr)
            return None

        transcript_id = transcript_response.json()['id']
        print(f"Transcription job started: {transcript_id}", file=sys.stderr)

        # Step 3: Poll for completion
        print("Waiting for transcription to complete...", file=sys.stderr)

        max_wait_time = 300  # 5 minutes max
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            status_response = requests.get(
                f"{ASSEMBLYAI_TRANSCRIPT_URL}/{transcript_id}",
                headers={'authorization': ASSEMBLYAI_API_KEY}
            )

            if status_response.status_code != 200:
                print(f"Status check failed: {status_response.status_code}", file=sys.stderr)
                return None

            result = status_response.json()
            status = result['status']

            if status == 'completed':
                print("Transcription completed successfully", file=sys.stderr)
                break
            elif status == 'error':
                print(f"Transcription failed: {result.get('error', 'Unknown error')}", file=sys.stderr)
                return None
            else:
                print(f"Transcription status: {status}", file=sys.stderr)
                time.sleep(5)  # Wait 5 seconds before checking again
        else:
            print("Transcription timeout", file=sys.stderr)
            return None

        # Step 4: Convert to our format
        segments = []
        if result.get('words'):
            # Group words into segments (similar to Whisper segments)
            current_segment = {"start": None, "end": None, "text": "", "words": []}
            words_per_segment = 10  # Adjust as needed
            word_count = 0

            for word in result['words']:
                if current_segment["start"] is None:
                    current_segment["start"] = word['start'] / 1000.0  # Convert ms to seconds

                current_segment["text"] += word['text'] + " "
                current_segment["words"].append(word)
                current_segment["end"] = word['end'] / 1000.0  # Convert ms to seconds
                word_count += 1

                # Create new segment after certain number of words or at sentence boundaries
                if word_count >= words_per_segment or word['text'].endswith(('.', '!', '?')):
                    segments.append({
                        "start": current_segment["start"],
                        "end": current_segment["end"],
                        "text": current_segment["text"].strip(),
                        "words": current_segment["words"]
                    })
                    current_segment = {"start": None, "end": None, "text": "", "words": []}
                    word_count = 0

            # Add remaining words as final segment
            if current_segment["text"].strip():
                segments.append({
                    "start": current_segment["start"],
                    "end": current_segment["end"],
                    "text": current_segment["text"].strip(),
                    "words": current_segment["words"]
                })

        print(f"Generated {len(segments)} transcript segments", file=sys.stderr)
        return segments

    except Exception as e:
        print(f"AssemblyAI transcription failed: {str(e)}", file=sys.stderr)
        return None

def detect_topic_boundaries(segments: List[Dict[str, Any]], min_segment_length: float = 5.0) -> List[float]:
    """
    Simple topic boundary detection based on sentence endings and pauses.
    Returns list of timestamps where topic boundaries are detected.
    """
    boundaries = [0.0]  # Always start with beginning

    for i, segment in enumerate(segments):
        text = segment["text"].strip()
        duration = segment["end"] - segment["start"]

        # Look for natural boundaries
        is_sentence_end = text.endswith(('.', '!', '?'))
        is_long_pause = i < len(segments) - 1 and segments[i + 1]["start"] - segment["end"] > 1.0
        is_question = '?' in text

        # Add boundary if we have indicators and minimum length is met
        if (is_sentence_end or is_long_pause or is_question):
            if len(boundaries) == 0 or segment["end"] - boundaries[-1] >= min_segment_length:
                boundaries.append(segment["end"])

    return boundaries

def detect_scenes_ai(video_path: str, content_threshold: float = 27.0, fade_threshold: float = 5.0) -> Dict[str, Any]:
    """
    AI-based scene detection combining visual cuts with speech analysis.
    Uses AssemblyAI for transcription and returns both scenes and full transcription.

    Args:
        video_path (str): Path to the video file
        content_threshold (float): Threshold for ContentDetector (cuts)
        fade_threshold (float): Threshold for ThresholdDetector (fades)

    Returns:
        dict: Dictionary containing scenes, full_transcription, and metadata
    """
    print("Starting AI-based segmentation with AssemblyAI transcription...", file=sys.stderr)

    # Step 1: Get traditional cut-based boundaries as candidates
    print("Getting visual cut boundaries...", file=sys.stderr)
    visual_scenes = detect_scenes(video_path, content_threshold, fade_threshold)
    if not visual_scenes:
        print("Warning: No visual scenes detected", file=sys.stderr)
        return {"scenes": [], "full_transcription": None, "transcription_metadata": None}

    # Step 2: Extract and transcribe audio using AssemblyAI
    print("Extracting audio for transcription...", file=sys.stderr)
    audio_path = extract_audio_for_transcription(video_path)

    transcript_segments = None
    topic_boundaries = []
    transcription_metadata = None

    if audio_path:
        try:
            # Transcribe audio using AssemblyAI
            transcript_segments = transcribe_audio_assemblyai(audio_path)

            if transcript_segments:
                print(f"Transcribed {len(transcript_segments)} speech segments", file=sys.stderr)

                # Create transcription metadata
                total_words = sum(len(seg.get('words', [])) for seg in transcript_segments)
                total_duration = max(seg['end'] for seg in transcript_segments) if transcript_segments else 0

                transcription_metadata = {
                    'language': 'en',  # AssemblyAI auto-detects, but we'll default to English
                    'duration': total_duration,
                    'word_count': total_words,
                    'segment_count': len(transcript_segments),
                    'transcription_method': 'assemblyai',
                    'created_at': time.time()
                }

                # Detect topic boundaries
                topic_boundaries = detect_topic_boundaries(transcript_segments)
                print(f"Detected {len(topic_boundaries)} topic boundaries", file=sys.stderr)

        finally:
            # Clean up temporary audio file
            if os.path.exists(audio_path):
                os.unlink(audio_path)

    # Step 3: Combine visual and speech boundaries
    print("Combining visual and speech boundaries...", file=sys.stderr)

    # Collect all potential boundaries
    all_boundaries = set()

    # Add visual boundaries
    for scene in visual_scenes:
        all_boundaries.add(scene["start"])
        all_boundaries.add(scene["end"])

    # Add topic boundaries
    for boundary in topic_boundaries:
        all_boundaries.add(boundary)

    # Sort boundaries
    sorted_boundaries = sorted(all_boundaries)

    # Create final scenes
    ai_scenes = []
    for i in range(len(sorted_boundaries) - 1):
        start_time = sorted_boundaries[i]
        end_time = sorted_boundaries[i + 1]

        # Skip very short segments (less than 2 seconds)
        if end_time - start_time < 2.0:
            continue

        # Find transcript for this segment
        segment_transcript = ""
        segment_topics = []

        if transcript_segments:
            segment_texts = []
            for seg in transcript_segments:
                # Check if segment overlaps with our scene
                if seg["start"] < end_time and seg["end"] > start_time:
                    segment_texts.append(seg["text"].strip())

            segment_transcript = " ".join(segment_texts).strip()

            # Simple topic extraction (first few words)
            if segment_transcript:
                words = segment_transcript.split()[:5]  # First 5 words as topic indicator
                if words:
                    segment_topics = [" ".join(words)]

        # Determine transition type (simplified)
        transition_type = "cut"  # Default

        # Check if this boundary came from topic detection
        if start_time in topic_boundaries:
            transition_type = "topic-change"

        scene = {
            "start": round(start_time, 2),
            "end": round(end_time, 2),
            "transition_type": transition_type,
            "transcript": segment_transcript,
            "topics": segment_topics,
            "confidence_score": 0.8 if segment_transcript else 0.6  # Higher confidence if we have speech
        }

        ai_scenes.append(scene)

    print(f"AI segmentation produced {len(ai_scenes)} scenes", file=sys.stderr)

    # Return both scenes and full transcription for reuse
    return {
        "scenes": ai_scenes,
        "full_transcription": transcript_segments,
        "transcription_metadata": transcription_metadata
    }

# --- END OF FILE ai_segmentation.py ---
